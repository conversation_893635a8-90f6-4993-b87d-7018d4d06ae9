<template>
  <div class="type-specific-dashboard">
    <!-- Innovator Dashboard -->
    <div v-if="profileType === 'innovator'" class="innovator-dashboard">
      <q-card class="dashboard-card">
        <q-card-section class="bg-green-8 text-white">
          <div class="text-h6">Innovator Dashboard</div>
          <div class="text-subtitle2">Tools and resources for innovators</div>
        </q-card-section>
        
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-green-1 rounded-borders">
                <q-item-section avatar>
                  <q-icon name="lightbulb" color="green-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Project Showcase</q-item-label>
                  <q-item-label caption>Create and manage your innovation projects</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-badge color="green" text-color="white">Coming Soon</q-badge>
                </q-item-section>
              </q-item>
            </div>
            
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-green-1 rounded-borders">
                <q-item-section avatar>
                  <q-icon name="search" color="green-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Find Investors</q-item-label>
                  <q-item-label caption>Connect with potential investors for your projects</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-badge color="green" text-color="white">Coming Soon</q-badge>
                </q-item-section>
              </q-item>
            </div>
            
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-green-1 rounded-borders">
                <q-item-section avatar>
                  <q-icon name="school" color="green-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Find Mentors</q-item-label>
                  <q-item-label caption>Connect with experienced mentors in your field</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-badge color="green" text-color="white">Coming Soon</q-badge>
                </q-item-section>
              </q-item>
            </div>
            
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-green-1 rounded-borders">
                <q-item-section avatar>
                  <q-icon name="attach_money" color="green-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Funding Opportunities</q-item-label>
                  <q-item-label caption>Discover grants and investment opportunities</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-badge color="green" text-color="white">Coming Soon</q-badge>
                </q-item-section>
              </q-item>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>
    
    <!-- Investor Dashboard -->
    <div v-else-if="profileType === 'investor'" class="investor-dashboard">
      <q-card class="dashboard-card">
        <q-card-section class="bg-blue-8 text-white">
          <div class="text-h6">Investor Dashboard</div>
          <div class="text-subtitle2">Tools and resources for investors</div>
        </q-card-section>
        
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-blue-1 rounded-borders">
                <q-item-section avatar>
                  <q-icon name="explore" color="blue-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Discover Projects</q-item-label>
                  <q-item-label caption>Browse innovative projects seeking investment</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-badge color="blue" text-color="white">Coming Soon</q-badge>
                </q-item-section>
              </q-item>
            </div>
            
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-blue-1 rounded-borders">
                <q-item-section avatar>
                  <q-icon name="assessment" color="blue-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Investment Portfolio</q-item-label>
                  <q-item-label caption>Track and manage your investments</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-badge color="blue" text-color="white">Coming Soon</q-badge>
                </q-item-section>
              </q-item>
            </div>
            
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-blue-1 rounded-borders">
                <q-item-section avatar>
                  <q-icon name="people" color="blue-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Connect with Innovators</q-item-label>
                  <q-item-label caption>Direct messaging with project creators</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-badge color="blue" text-color="white">Coming Soon</q-badge>
                </q-item-section>
              </q-item>
            </div>
            
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-blue-1 rounded-borders">
                <q-item-section avatar>
                  <q-icon name="trending_up" color="blue-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Market Trends</q-item-label>
                  <q-item-label caption>Insights and analytics on innovation trends</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-badge color="blue" text-color="white">Coming Soon</q-badge>
                </q-item-section>
              </q-item>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>
    
    <!-- Mentor Dashboard -->
    <div v-else-if="profileType === 'mentor'" class="mentor-dashboard">
      <q-card class="dashboard-card">
        <q-card-section class="bg-purple-8 text-white">
          <div class="text-h6">Mentor Dashboard</div>
          <div class="text-subtitle2">Tools and resources for mentors</div>
        </q-card-section>
        
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-purple-1 rounded-borders" @click="handleAITrigger('mentorship_opportunities')">
                <q-item-section avatar>
                  <q-icon name="people" color="purple-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Mentorship Opportunities</q-item-label>
                  <q-item-label caption>Find innovators seeking mentorship</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn
                    color="purple"
                    text-color="white"
                    icon="psychology"
                    size="sm"
                    round
                    @click.stop="handleAITrigger('mentorship_opportunities')"
                  >
                    <q-tooltip>Ask AI Assistant</q-tooltip>
                  </q-btn>
                </q-item-section>
              </q-item>
            </div>

            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-purple-1 rounded-borders" @click="handleAITrigger('mentorship_sessions')">
                <q-item-section avatar>
                  <q-icon name="event" color="purple-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Mentorship Sessions</q-item-label>
                  <q-item-label caption>Schedule and manage mentoring sessions</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn
                    color="purple"
                    text-color="white"
                    icon="psychology"
                    size="sm"
                    round
                    @click.stop="handleAITrigger('mentorship_sessions')"
                  >
                    <q-tooltip>Ask AI Assistant</q-tooltip>
                  </q-btn>
                </q-item-section>
              </q-item>
            </div>

            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-purple-1 rounded-borders" @click="handleAITrigger('mentor_community')">
                <q-item-section avatar>
                  <q-icon name="forum" color="purple-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Mentor Community</q-item-label>
                  <q-item-label caption>Connect with other mentors</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn
                    color="purple"
                    text-color="white"
                    icon="psychology"
                    size="sm"
                    round
                    @click.stop="handleAITrigger('mentor_community')"
                  >
                    <q-tooltip>Ask AI Assistant</q-tooltip>
                  </q-btn>
                </q-item-section>
              </q-item>
            </div>

            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-purple-1 rounded-borders" @click="handleAITrigger('impact_tracking')">
                <q-item-section avatar>
                  <q-icon name="insights" color="purple-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Impact Tracking</q-item-label>
                  <q-item-label caption>Track your mentorship impact</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn
                    color="purple"
                    text-color="white"
                    icon="psychology"
                    size="sm"
                    round
                    @click.stop="handleAITrigger('impact_tracking')"
                  >
                    <q-tooltip>Ask AI Assistant</q-tooltip>
                  </q-btn>
                </q-item-section>
              </q-item>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Student Dashboard -->
    <div v-else-if="profileType === 'student'" class="student-dashboard">
      <q-card class="dashboard-card">
        <q-card-section class="bg-orange-8 text-white">
          <div class="text-h6">Student Dashboard</div>
          <div class="text-subtitle2">Tools and resources for students</div>
        </q-card-section>

        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-orange-1 rounded-borders" @click="handleAITrigger('learning_opportunities')">
                <q-item-section avatar>
                  <q-icon name="school" color="orange-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Learning Opportunities</q-item-label>
                  <q-item-label caption>Find courses and educational resources</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn
                    color="orange"
                    text-color="white"
                    icon="psychology"
                    size="sm"
                    round
                    @click.stop="handleAITrigger('learning_opportunities')"
                  >
                    <q-tooltip>Ask AI Assistant</q-tooltip>
                  </q-btn>
                </q-item-section>
              </q-item>
            </div>

            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-orange-1 rounded-borders" @click="handleAITrigger('mentorship_matching')">
                <q-item-section avatar>
                  <q-icon name="connect_without_contact" color="orange-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Find Mentors</q-item-label>
                  <q-item-label caption>Connect with experienced mentors</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn
                    color="orange"
                    text-color="white"
                    icon="psychology"
                    size="sm"
                    round
                    @click.stop="handleAITrigger('mentorship_matching')"
                  >
                    <q-tooltip>Ask AI Assistant</q-tooltip>
                  </q-btn>
                </q-item-section>
              </q-item>
            </div>

            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-orange-1 rounded-borders" @click="handleAITrigger('career_guidance')">
                <q-item-section avatar>
                  <q-icon name="work" color="orange-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Career Guidance</q-item-label>
                  <q-item-label caption>Get advice on career development</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn
                    color="orange"
                    text-color="white"
                    icon="psychology"
                    size="sm"
                    round
                    @click.stop="handleAITrigger('career_guidance')"
                  >
                    <q-tooltip>Ask AI Assistant</q-tooltip>
                  </q-btn>
                </q-item-section>
              </q-item>
            </div>

            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-orange-1 rounded-borders" @click="handleAITrigger('networking_events')">
                <q-item-section avatar>
                  <q-icon name="event" color="orange-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Networking Events</q-item-label>
                  <q-item-label caption>Discover networking opportunities</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn
                    color="orange"
                    text-color="white"
                    icon="psychology"
                    size="sm"
                    round
                    @click.stop="handleAITrigger('networking_events')"
                  >
                    <q-tooltip>Ask AI Assistant</q-tooltip>
                  </q-btn>
                </q-item-section>
              </q-item>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Professional Dashboard -->
    <div v-else-if="profileType === 'professional'" class="professional-dashboard">
      <q-card class="dashboard-card">
        <q-card-section class="bg-teal-8 text-white">
          <div class="text-h6">Professional Dashboard</div>
          <div class="text-subtitle2">Tools and resources for professionals</div>
        </q-card-section>

        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-teal-1 rounded-borders" @click="handleAITrigger('collaboration_opportunities')">
                <q-item-section avatar>
                  <q-icon name="handshake" color="teal-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Collaboration Opportunities</q-item-label>
                  <q-item-label caption>Find partners and projects</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn
                    color="teal"
                    text-color="white"
                    icon="psychology"
                    size="sm"
                    round
                    @click.stop="handleAITrigger('collaboration_opportunities')"
                  >
                    <q-tooltip>Ask AI Assistant</q-tooltip>
                  </q-btn>
                </q-item-section>
              </q-item>
            </div>

            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-teal-1 rounded-borders" @click="handleAITrigger('skill_development')">
                <q-item-section avatar>
                  <q-icon name="trending_up" color="teal-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Skill Development</q-item-label>
                  <q-item-label caption>Enhance your professional skills</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn
                    color="teal"
                    text-color="white"
                    icon="psychology"
                    size="sm"
                    round
                    @click.stop="handleAITrigger('skill_development')"
                  >
                    <q-tooltip>Ask AI Assistant</q-tooltip>
                  </q-btn>
                </q-item-section>
              </q-item>
            </div>

            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-teal-1 rounded-borders" @click="handleAITrigger('industry_insights')">
                <q-item-section avatar>
                  <q-icon name="insights" color="teal-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Industry Insights</q-item-label>
                  <q-item-label caption>Stay updated with trends</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn
                    color="teal"
                    text-color="white"
                    icon="psychology"
                    size="sm"
                    round
                    @click.stop="handleAITrigger('industry_insights')"
                  >
                    <q-tooltip>Ask AI Assistant</q-tooltip>
                  </q-btn>
                </q-item-section>
              </q-item>
            </div>

            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-teal-1 rounded-borders" @click="handleAITrigger('leadership_development')">
                <q-item-section avatar>
                  <q-icon name="emoji_people" color="teal-8" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Leadership Development</q-item-label>
                  <q-item-label caption>Advance your leadership skills</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn
                    color="teal"
                    text-color="white"
                    icon="psychology"
                    size="sm"
                    round
                    @click.stop="handleAITrigger('leadership_development')"
                  >
                    <q-tooltip>Ask AI Assistant</q-tooltip>
                  </q-btn>
                </q-item-section>
              </q-item>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Default Dashboard for other profile types -->
    <div v-else class="default-dashboard">
      <q-card class="dashboard-card">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">{{ formatProfileType }} Dashboard</div>
          <div class="text-subtitle2">Tools and resources tailored for you</div>
        </q-card-section>
        
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-grey-2 rounded-borders">
                <q-item-section avatar>
                  <q-icon name="explore" color="primary" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Discover Opportunities</q-item-label>
                  <q-item-label caption>Find relevant opportunities in the ecosystem</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-badge color="primary" text-color="white">Coming Soon</q-badge>
                </q-item-section>
              </q-item>
            </div>
            
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-grey-2 rounded-borders">
                <q-item-section avatar>
                  <q-icon name="people" color="primary" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Network</q-item-label>
                  <q-item-label caption>Connect with other members of the ecosystem</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-badge color="primary" text-color="white">Coming Soon</q-badge>
                </q-item-section>
              </q-item>
            </div>
            
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-grey-2 rounded-borders">
                <q-item-section avatar>
                  <q-icon name="event" color="primary" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Events</q-item-label>
                  <q-item-label caption>Discover and register for upcoming events</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-badge color="primary" text-color="white">Coming Soon</q-badge>
                </q-item-section>
              </q-item>
            </div>
            
            <div class="col-12 col-md-6">
              <q-item clickable v-ripple class="dashboard-item bg-grey-2 rounded-borders">
                <q-item-section avatar>
                  <q-icon name="library_books" color="primary" size="md" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Resources</q-item-label>
                  <q-item-label caption>Access knowledge resources and tools</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-badge color="primary" text-color="white">Coming Soon</q-badge>
                </q-item-section>
              </q-item>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { formatProfileType as formatType } from '../../services/profileTypes';
import AIChatTriggerService from '../../services/aiChatTriggerService';

const props = defineProps<{
  profileType: string | null;
}>();

const formatProfileType = computed(() => {
  return props.profileType ? formatType(props.profileType) : 'User';
});

// Handle AI trigger clicks
const handleAITrigger = async (triggerKey: string) => {
  try {
    await AIChatTriggerService.triggerChat(triggerKey, props.profileType || undefined);
  } catch (error) {
    console.error('Error triggering AI chat:', error);
  }
};
</script>

<style scoped>
.dashboard-card {
  height: 100%;
}

.dashboard-item {
  margin-bottom: 8px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.dashboard-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}
</style>
