import "jsr:@supabase/functions-js/edge-runtime.d.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

const DEEPSEEK_API_KEY = '***********************************';
const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';

Deno.serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { message } = await req.json();
    
    console.log('=== Testing DeepSeek API ===');
    console.log('Message:', message);
    console.log('API Key:', DEEPSEEK_API_KEY ? 'Present' : 'Missing');

    const response = await fetch(DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          { role: 'user', content: message || 'Hello, can you respond with "API working"?' }
        ],
        max_tokens: 100,
        temperature: 0.7
      })
    });

    console.log('DeepSeek Response Status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('DeepSeek Error:', errorText);
      
      return new Response(
        JSON.stringify({
          response: "I'm currently experiencing a system upgrade. Please try again in a moment.",
          actions: [],
          suggestions: ["How can I help you?", "Tell me about the platform", "What features are available?"],
          conversation_id: crypto.randomUUID()
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const data = await response.json();
    const aiResponse = data.choices?.[0]?.message?.content || 'No response';

    console.log('AI Response:', aiResponse);

    return new Response(
      JSON.stringify({
        response: aiResponse,
        actions: [
          {
            type: 'navigate',
            label: 'Explore Platform',
            action: '/dashboard',
            data: { route: '/dashboard' }
          }
        ],
        suggestions: ["How can I help you?", "Tell me about the platform", "What features are available?"],
        conversation_id: crypto.randomUUID()
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error: any) {
    console.error('Function Error:', error);
    
    return new Response(
      JSON.stringify({
        response: "I'm currently experiencing a system upgrade. Please try again in a moment.",
        actions: [],
        suggestions: ["How can I help you?", "Tell me about the platform", "What features are available?"],
        conversation_id: crypto.randomUUID()
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
