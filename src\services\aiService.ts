/**
 * Unified AI Service
 * 
 * Consolidated AI service that combines chat functionality, conversation persistence,
 * and enhanced context awareness in a single, clean interface.
 */

import { supabase } from '../lib/supabase';
import { triggerAuthDialog } from './authDialogService';
import { validateRouteForCTA, logRouteValidationFailure, isRouteSafe } from './routeValidationService';

// Core Types
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  actions?: ActionButton[];
  suggestions?: string[];
}

export interface ActionButton {
  id: string;
  label: string;
  type: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  action: string;
  url?: string;
  requiresAuth?: boolean;
}

export interface UserContext {
  is_authenticated: boolean;
  profile_type?: string;
  profile_completion?: number;
  current_page: string;
  user_id?: string;
  profile_data?: any;
}

export interface ChatRequest {
  message: string;
  conversation_history: Array<{ role: string; content: string }>;
  user_context: UserContext;
}

export interface ChatResponse {
  response: string;
  actions?: ActionButton[];
  suggestions?: string[];
  conversation_id?: string;
  error?: string;
}

// Conversation Persistence Types
export interface AIConversation {
  id: string;
  user_id: string;
  title?: string;
  summary?: string;
  context_data: Record<string, any>;
  message_count: number;
  last_message_at?: string;
  created_at: string;
  updated_at: string;
}

export interface AIMessage {
  id: string;
  conversation_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata: Record<string, any>;
  actions: any[];
  suggestions: any[];
  created_at: string;
}

// Error Handling
export class AIServiceError extends Error {
  constructor(
    message: string,
    public type: string = 'unknown',
    public details?: string
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}

/**
 * Unified AI Service Class
 */
export class AIService {
  
  /**
   * Send a chat message with streaming support
   */
  static async sendChatMessage(
    request: ChatRequest,
    onChunk?: (content: string) => void,
    onComplete?: (actions: ActionButton[], suggestions: string[]) => void
  ): Promise<ChatMessage> {
    try {
      console.log('Sending AI chat message:', request);

      if (onChunk) {
        // Use streaming
        await this.sendChatMessageStream(request, onChunk, onComplete || (() => {}));
        
        // Return the last message from the response
        const lastMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: '', // Will be filled by streaming
          timestamp: new Date(),
          actions: [],
          suggestions: []
        };
        
        return lastMessage;
      } else {
        // Use regular request with ai-enhanced-chat
        const { data, error } = await supabase.functions.invoke('ai-enhanced-chat', {
          body: {
            message: request.message,
            conversation_history: request.conversation_history || [],
            user_context: request.user_context,
            include_detailed_context: true
          }
        });

        if (error) {
          throw new AIServiceError(`AI service error: ${error.message}`, 'service_error');
        }

        return {
          id: Date.now().toString(),
          role: 'assistant',
          content: data.response || 'I apologize, but I couldn\'t generate a response. Please try again.',
          timestamp: new Date(),
          actions: data.actions || [],
          suggestions: data.suggestions || []
        };
      }
    } catch (error: any) {
      console.error('AI Service Error:', error);
      throw new AIServiceError(
        error.message || 'Failed to send chat message',
        error.type || 'unknown',
        error.details
      );
    }
  }

  /**
   * Send chat message with streaming
   */
  static async sendChatMessageStream(
    request: ChatRequest,
    onChunk: (content: string) => void,
    onComplete: (actions: ActionButton[], suggestions: string[]) => void
  ): Promise<void> {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const token = session?.access_token || supabase.supabaseKey;

      const response = await fetch(`${supabase.supabaseUrl}/functions/v1/ai-enhanced-chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify({
          message: request.message,
          conversation_history: request.conversation_history || [],
          user_context: request.user_context,
          include_detailed_context: true
        })
      });

      if (!response.ok) {
        throw new AIServiceError(`HTTP ${response.status}: ${response.statusText}`, 'http_error');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new AIServiceError('No response body available', 'stream_error');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let actions: ActionButton[] = [];
      let suggestions: string[] = [];

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);

              if (data.type === 'content' && data.content) {
                onChunk(data.content);
              } else if (data.type === 'done') {
                // Generate contextual actions and suggestions when streaming is done
                actions = this.generateContextualActions({
                  is_authenticated: true, // We'll get this from the request context
                  current_page: 'chat'
                });
                suggestions = this.generateContextualSuggestions({
                  is_authenticated: true,
                  current_page: 'chat'
                });
              } else if (data.type === 'error') {
                throw new Error(data.error || 'Streaming error');
              }
            } catch (parseError) {
              console.warn('Failed to parse streaming data:', parseError);
            }
          }
        }
      }

      onComplete(actions, suggestions);
    } catch (error: any) {
      console.error('Streaming AI Service Error:', error);
      throw new AIServiceError(
        error.message || 'Failed to stream chat message',
        error.type || 'stream_error'
      );
    }
  }

  /**
   * Create a new conversation
   */
  static async createConversation(
    userId: string,
    title?: string,
    contextData: Record<string, any> = {}
  ): Promise<AIConversation> {
    const { data, error } = await supabase
      .from('ai_conversations')
      .insert({
        user_id: userId,
        title,
        context_data: contextData
      })
      .select()
      .single();

    if (error) {
      throw new AIServiceError(`Failed to create conversation: ${error.message}`);
    }

    return data;
  }

  /**
   * Get user conversations
   */
  static async getUserConversations(userId: string, limit = 10): Promise<AIConversation[]> {
    const { data, error } = await supabase
      .from('ai_conversations')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw new AIServiceError(`Failed to get conversations: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Add message to conversation
   */
  static async addMessage(
    conversationId: string,
    role: 'user' | 'assistant' | 'system',
    content: string,
    options: {
      metadata?: Record<string, any>;
      actions?: any[];
      suggestions?: any[];
    } = {}
  ): Promise<AIMessage> {
    const { data, error } = await supabase
      .from('ai_messages')
      .insert({
        conversation_id: conversationId,
        role,
        content,
        metadata: options.metadata || {},
        actions: options.actions || [],
        suggestions: options.suggestions || []
      })
      .select()
      .single();

    if (error) {
      throw new AIServiceError(`Failed to add message: ${error.message}`);
    }

    return data;
  }

  /**
   * Get conversation messages
   */
  static async getConversationMessages(conversationId: string): Promise<AIMessage[]> {
    const { data, error } = await supabase
      .from('ai_messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true });

    if (error) {
      throw new AIServiceError(`Failed to get messages: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get or create conversation for user
   */
  static async getOrCreateUserConversation(
    userId: string,
    contextData: Record<string, any> = {}
  ): Promise<AIConversation> {
    const conversations = await this.getUserConversations(userId, 1);

    if (conversations.length > 0) {
      const lastConversation = conversations[0];
      const lastMessageTime = lastConversation.last_message_at
        ? new Date(lastConversation.last_message_at)
        : new Date(lastConversation.created_at);

      const hoursSinceLastMessage = (Date.now() - lastMessageTime.getTime()) / (1000 * 60 * 60);

      if (hoursSinceLastMessage < 24) {
        return lastConversation;
      }
    }

    return this.createConversation(userId, undefined, contextData);
  }

  /**
   * Execute an action button
   */
  static async executeAction(action: ActionButton): Promise<void> {
    console.log('Executing action:', action);

    try {
      switch (action.action) {
        case 'navigate':
          if (action.url && isRouteSafe(action.url)) {
            // Handle navigation using proper routing
            if (action.url.startsWith('http')) {
              // External URL
              window.open(action.url, '_blank');
            } else {
              // Internal route - use proper navigation
              const router = await import('vue-router');
              if (router.useRouter) {
                const routerInstance = router.useRouter();
                routerInstance.push(action.url);
              } else {
                // Fallback to location change without hash
                window.location.href = action.url;
              }
            }
          }
          break;
        case 'auth':
          if (action.url?.includes('signup')) {
            triggerAuthDialog('signup');
          } else if (action.url?.includes('login')) {
            triggerAuthDialog('login');
          } else {
            triggerAuthDialog('login');
          }
          break;
        case 'external':
          if (action.url) {
            window.open(action.url, '_blank');
          }
          break;
        default:
          await this.handlePlatformAction(action.action);
      }
    } catch (error) {
      console.error('Error executing action:', error);
      throw error;
    }
  }

  /**
   * Handle platform-specific actions
   */
  private static async handlePlatformAction(actionType: string): Promise<void> {
    switch (actionType) {
      case 'login':
      case 'signup':
        triggerAuthDialog(actionType);
        break;
      case 'complete-profile':
        try {
          const router = await import('vue-router');
          if (router.useRouter) {
            const routerInstance = router.useRouter();
            routerInstance.push('/profile/edit');
          } else {
            window.location.href = '/profile/edit';
          }
        } catch {
          window.location.href = '/profile/edit';
        }
        break;
      case 'create-post':
        try {
          const router = await import('vue-router');
          if (router.useRouter) {
            const routerInstance = router.useRouter();
            routerInstance.push('/virtual-community');
          } else {
            window.location.href = '/virtual-community';
          }
        } catch {
          window.location.href = '/virtual-community';
        }
        break;
      case 'navigate':
        // Already handled in executeAction
        break;
      case 'auth':
        // Already handled in executeAction
        break;
      default:
        console.warn('Unknown platform action:', actionType);
    }
  }

  /**
   * Generate contextual action buttons based on user context
   */
  private static generateContextualActions(userContext: UserContext): ActionButton[] {
    const actions: ActionButton[] = [];

    if (!userContext.is_authenticated) {
      actions.push(
        {
          type: 'action',
          label: 'Sign Up',
          icon: 'person_add',
          action: 'signup',
          color: 'primary'
        },
        {
          type: 'action',
          label: 'Sign In',
          icon: 'login',
          action: 'login',
          color: 'secondary'
        }
      );
    } else {
      // Authenticated user actions
      if ((userContext.profile_completion || 0) < 80) {
        actions.push({
          type: 'navigation',
          label: 'Complete Profile',
          icon: 'person',
          route: '/dashboard/profile',
          color: 'primary'
        });
      }

      actions.push(
        {
          type: 'navigation',
          label: 'View Dashboard',
          icon: 'dashboard',
          route: '/dashboard',
          color: 'secondary'
        },
        {
          type: 'navigation',
          label: 'Explore Community',
          icon: 'groups',
          route: '/virtual-community',
          color: 'info'
        }
      );
    }

    return actions.slice(0, 3); // Limit to 3 actions
  }

  /**
   * Generate contextual suggestions based on user context
   */
  private static generateContextualSuggestions(userContext: UserContext): string[] {
    const suggestions: string[] = [];

    if (!userContext.is_authenticated) {
      suggestions.push(
        "How do I sign up for the platform?",
        "What features are available?",
        "Tell me about the innovation community",
        "How can I connect with investors?"
      );
    } else {
      // Authenticated user suggestions
      if ((userContext.profile_completion || 0) < 80) {
        suggestions.push("How can I complete my profile?");
      }

      switch (userContext.profile_type) {
        case 'innovator':
          suggestions.push(
            "How do I showcase my innovation?",
            "Connect me with potential investors",
            "Find mentorship opportunities"
          );
          break;
        case 'investor':
          suggestions.push(
            "Show me promising innovations",
            "How do I evaluate startups?",
            "Find investment opportunities"
          );
          break;
        case 'mentor':
          suggestions.push(
            "How can I offer mentorship?",
            "Find innovators to mentor",
            "Share my expertise effectively"
          );
          break;
        default:
          suggestions.push(
            "How can I improve my profile?",
            "Show me networking opportunities",
            "What's new in Zimbabwe's innovation scene?"
          );
      }
    }

    return suggestions.slice(0, 4); // Limit to 4 suggestions
  }
}

// Export default instance
export default AIService;
