import { createApp } from "vue";
import { createP<PERSON> } from "pinia";
import { Quasar } from "quasar";
// Import Quasar plugins
import { Dialog } from 'quasar';
import { Notify } from 'quasar';
import { Loading } from 'quasar';
import router from "./router";
import App from "./App.vue";
import VueCountdown from '@chenfengyuan/vue-countdown';
// Use only the unified icon plugin for better performance
import unifiedIconPlugin from "./plugins/unified-icon";
import passiveEventsPlugin from "./plugins/passive-events";
import analyticsPlugin from "./plugins/analytics";
import securityPlugin from "./plugins/security";
import { createServiceCoordination } from "./plugins/serviceCoordination";
import { getFinalServiceCoordinationConfig } from "./config/serviceCoordinationConfig";
import { initializeDevelopmentTools } from "./utils/developmentTools";
import { createGlobalErrorHandler } from "./utils/globalErrorHandler";
import "./utils/validateMainAppIntegration";
// Import custom directives
import { scrollToTop, scrollToTopOnUpdate } from './directives/scrollToTop';
// Import utility functions

// Import Quasar css
import "quasar/dist/quasar.css";

// Import custom icons
import "./assets/icons/custom-icons.css";

// Import icon libraries
import "@quasar/extras/material-icons/material-icons.css";
import "@quasar/extras/material-icons-outlined/material-icons-outlined.css";
import "@quasar/extras/fontawesome-v6/fontawesome-v6.css";
import "@quasar/extras/mdi-v6/mdi-v6.css";
import "@quasar/extras/ionicons-v4/ionicons-v4.css";

// Import application CSS
import "./style.css";

// Import icon fixes
import "./assets/css/icon-fixes.css";
import "./assets/css/material-icons.css";
import "./assets/css/refined-tabs.css";

// Import mega menu styles
import "./css/mega-menu.css";

const app = createApp(App);

// Initialize Pinia store first
const pinia = createPinia();
app.use(pinia);
app.use(Quasar, {
  plugins: { Dialog, Notify, Loading },
  config: {
    brand: {
      primary: '#0D8A3E',
      secondary: '#dfefe6',
      accent: '#F5A623',
    },
    htmlVariables: {
      'font-family-base': "'Rubik', sans-serif"
    },
    select: {
      behavior: 'menu'
    },
    // Override default Quasar component props
    components: {
      QSelect: {
        dropdownIcon: 'img:data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"%3E%3Cpath fill="%230D8A3E" d="M7 10l5 5 5-5z"/%3E%3C/svg%3E'
      },
      QStepper: {
        doneIcon: 'img:data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"%3E%3Cpath fill="%230D8A3E" d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/%3E%3C/svg%3E',
        activeIcon: 'img:data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"%3E%3Cpath fill="%230D8A3E" d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/%3E%3C/svg%3E',
        errorIcon: 'img:data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"%3E%3Cpath fill="%230D8A3E" d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/%3E%3C/svg%3E'
      }
    }
  },
  extras: [
    'material-icons',
    'fontawesome-v6',
    'mdi-v6',
    'ionicons-v4'
  ]
});
// Initialize global error handling (must be early in the setup)
const globalErrorHandler = createGlobalErrorHandler();
app.use(globalErrorHandler);

// Initialize service coordination (must be after Pinia and error handling)
const serviceCoordinationConfig = getFinalServiceCoordinationConfig();
const serviceCoordination = createServiceCoordination(serviceCoordinationConfig);

app.use(serviceCoordination);

// Initialize our custom plugins
app.use(securityPlugin); // Initialize security first
app.use(unifiedIconPlugin);
app.use(passiveEventsPlugin);
app.use(analyticsPlugin, { router });

// Initialize router
app.use(router);

// Register the countdown component globally
app.component('vue-countdown', VueCountdown);

// Register global directives
app.directive('scroll-to-top', scrollToTop);
app.directive('scroll-to-top-on-update', scrollToTopOnUpdate);

// Profile tables are now managed by the profile service

// Initialize the application
console.log('🚀 Initializing ZB Innovation Platform...');

try {
  app.mount("#app");
  console.log('✅ Application mounted successfully');

  // Initialize development tools and log service status in development
  if (process.env.NODE_ENV === 'development') {
    // Initialize development tools
    initializeDevelopmentTools();

    setTimeout(() => {
      const coordinator = serviceCoordination.coordinator;
      const health = coordinator.getServiceHealth();
      console.log('🔧 Service Health Status:', health);
      console.log('🛠️ Development tools are ready! Use __SERVICE_DIAGNOSTICS__() to run diagnostics.');
      console.log('🚨 Error handling is active! Use __ERROR_HANDLER__ to view error statistics.');
      console.log('🧪 Integration validation available! Use __validateMainAppIntegration() to test everything.');
    }, 2000); // Check after 2 seconds to allow initialization
  }
} catch (error) {
  console.error('❌ Failed to mount application:', error);

  // Show user-friendly error message
  document.body.innerHTML = `
    <div style="
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      font-family: 'Rubik', sans-serif;
      background: #f5f5f5;
      color: #333;
      text-align: center;
    ">
      <div>
        <h1 style="color: #0D8A3E; margin-bottom: 20px;">Application Error</h1>
        <p>We're experiencing technical difficulties. Please refresh the page or try again later.</p>
        <button onclick="window.location.reload()" style="
          background: #0D8A3E;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 5px;
          cursor: pointer;
          margin-top: 20px;
        ">Refresh Page</button>
      </div>
    </div>
  `;
}



