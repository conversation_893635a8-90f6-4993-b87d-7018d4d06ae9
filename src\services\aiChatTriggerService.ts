/**
 * AI Chat Trigger Service
 * 
 * Centralized service for triggering AI chat with predefined messages
 * based on user context, profile types, and platform features.
 */

import { useAIChatStore } from '../stores/aiChatStore';

export interface AITriggerContext {
  profileType?: string;
  feature?: string;
  context?: string;
  userAction?: string;
  additionalData?: Record<string, any>;
}

export interface AITriggerMessage {
  message: string;
  context: AITriggerContext;
  suggestions?: string[];
}

/**
 * Profile-specific AI trigger messages
 */
export const PROFILE_TRIGGERS = {
  innovator: {
    project_showcase: {
      message: "I want to showcase my innovation projects effectively. Can you help me create compelling project presentations and find the right platforms to display my work?",
      context: { profileType: 'innovator', feature: 'project_showcase' },
      suggestions: [
        "How do I create a compelling project showcase?",
        "What platforms should I use?",
        "How do I highlight my innovation's impact?"
      ]
    },
    find_investors: {
      message: "I'm looking for investors for my innovation project. Can you help me identify potential investors and guide me through the process of connecting with them?",
      context: { profileType: 'innovator', feature: 'find_investors' },
      suggestions: [
        "Find investors in my industry",
        "How do I pitch to investors?",
        "What do investors look for?"
      ]
    },
    find_mentors: {
      message: "I need experienced mentors to guide my innovation journey. Can you help me find mentors who have expertise in my field and understand the challenges I'm facing?",
      context: { profileType: 'innovator', feature: 'find_mentors' },
      suggestions: [
        "Find mentors in my field",
        "How do I approach potential mentors?",
        "What should I ask my mentor?"
      ]
    },
    funding_opportunities: {
      message: "I'm seeking funding opportunities for my innovation project. Can you help me discover grants, competitions, and investment opportunities that match my project?",
      context: { profileType: 'innovator', feature: 'funding_opportunities' },
      suggestions: [
        "Show me available grants",
        "Find innovation competitions",
        "How do I apply for funding?"
      ]
    }
  },
  investor: {
    discover_projects: {
      message: "I want to discover promising innovation projects to invest in. Can you help me find projects that align with my investment criteria and interests?",
      context: { profileType: 'investor', feature: 'discover_projects' },
      suggestions: [
        "Show me investment opportunities",
        "How do I evaluate projects?",
        "What sectors are trending?"
      ]
    },
    investment_portfolio: {
      message: "I need help managing and tracking my investment portfolio. Can you provide guidance on portfolio management and performance tracking for innovation investments?",
      context: { profileType: 'investor', feature: 'investment_portfolio' },
      suggestions: [
        "How do I track portfolio performance?",
        "What metrics should I monitor?",
        "How do I diversify my investments?"
      ]
    },
    connect_innovators: {
      message: "I want to connect directly with innovators and project creators. Can you help me find ways to engage with entrepreneurs and build meaningful relationships?",
      context: { profileType: 'investor', feature: 'connect_innovators' },
      suggestions: [
        "Find innovators to connect with",
        "How do I approach entrepreneurs?",
        "What networking events are available?"
      ]
    },
    market_trends: {
      message: "I need insights on market trends and innovation analytics. Can you provide me with current market data and trends that could inform my investment decisions?",
      context: { profileType: 'investor', feature: 'market_trends' },
      suggestions: [
        "Show me market trends",
        "What sectors are growing?",
        "How do I analyze market opportunities?"
      ]
    }
  },
  mentor: {
    mentorship_opportunities: {
      message: "I'd like to explore mentorship opportunities. Can you help me find innovators who could benefit from my expertise and guidance?",
      context: { profileType: 'mentor', feature: 'mentorship_opportunities' },
      suggestions: [
        "Show me potential mentees",
        "How do I create a mentorship program?",
        "What are the best practices for mentoring?"
      ]
    },
    mentorship_sessions: {
      message: "I want to schedule and manage mentoring sessions. Can you guide me through the process and show me available tools?",
      context: { profileType: 'mentor', feature: 'mentorship_sessions' },
      suggestions: [
        "How do I schedule sessions?",
        "What tools are available for mentoring?",
        "How do I track mentee progress?"
      ]
    },
    mentor_community: {
      message: "I'd like to connect with other mentors to share experiences and best practices. How can I join the mentor community?",
      context: { profileType: 'mentor', feature: 'mentor_community' },
      suggestions: [
        "Find other mentors",
        "Join mentor discussions",
        "Share mentoring resources"
      ]
    },
    impact_tracking: {
      message: "I want to track the impact of my mentorship activities. Can you show me how to measure and document my mentoring success?",
      context: { profileType: 'mentor', feature: 'impact_tracking' },
      suggestions: [
        "How do I measure impact?",
        "What metrics should I track?",
        "How do I create impact reports?"
      ]
    }
  },
  student: {
    learning_opportunities: {
      message: "I'm looking for learning opportunities and educational resources. Can you help me find courses, workshops, and skill development programs?",
      context: { profileType: 'student', feature: 'learning_opportunities' },
      suggestions: [
        "Find online courses",
        "Show me workshops",
        "What skills should I develop?"
      ]
    },
    mentorship_matching: {
      message: "I need a mentor to guide my learning journey. Can you help me find the right mentor based on my interests and goals?",
      context: { profileType: 'student', feature: 'mentorship_matching' },
      suggestions: [
        "Find a mentor",
        "How do I approach mentors?",
        "What should I ask my mentor?"
      ]
    },
    career_guidance: {
      message: "I need career guidance and advice on my professional development. Can you help me explore career paths and opportunities?",
      context: { profileType: 'student', feature: 'career_guidance' },
      suggestions: [
        "Explore career paths",
        "How do I build my resume?",
        "What internships are available?"
      ]
    },
    networking_events: {
      message: "I want to attend networking events and connect with professionals in my field. Can you show me upcoming events and networking opportunities?",
      context: { profileType: 'student', feature: 'networking_events' },
      suggestions: [
        "Find networking events",
        "How do I network effectively?",
        "Connect with professionals"
      ]
    }
  },
  professional: {
    collaboration_opportunities: {
      message: "I'm looking for collaboration opportunities with other professionals and organizations. Can you help me find potential partners?",
      context: { profileType: 'professional', feature: 'collaboration_opportunities' },
      suggestions: [
        "Find collaboration partners",
        "How do I propose partnerships?",
        "What projects need professionals?"
      ]
    },
    skill_development: {
      message: "I want to enhance my professional skills and stay updated with industry trends. Can you recommend development opportunities?",
      context: { profileType: 'professional', feature: 'skill_development' },
      suggestions: [
        "Find professional courses",
        "What skills are in demand?",
        "How do I upskill effectively?"
      ]
    },
    industry_insights: {
      message: "I need insights about industry trends and market opportunities. Can you provide analysis and recommendations for my field?",
      context: { profileType: 'professional', feature: 'industry_insights' },
      suggestions: [
        "Show industry trends",
        "What are market opportunities?",
        "How do I stay competitive?"
      ]
    },
    leadership_development: {
      message: "I want to develop my leadership skills and advance my career. Can you guide me through leadership development opportunities?",
      context: { profileType: 'professional', feature: 'leadership_development' },
      suggestions: [
        "Find leadership programs",
        "How do I become a better leader?",
        "What leadership styles work best?"
      ]
    }
  },
  industry_expert: {
    knowledge_sharing: {
      message: "I want to share my industry expertise and knowledge with the community. Can you help me find platforms and opportunities to contribute?",
      context: { profileType: 'industry_expert', feature: 'knowledge_sharing' },
      suggestions: [
        "How do I share expertise?",
        "Find speaking opportunities",
        "Create educational content"
      ]
    },
    consulting_opportunities: {
      message: "I'm interested in providing consulting services to startups and organizations. Can you help me find consulting opportunities?",
      context: { profileType: 'industry_expert', feature: 'consulting_opportunities' },
      suggestions: [
        "Find consulting projects",
        "How do I price my services?",
        "Connect with startups needing advice"
      ]
    },
    research_collaboration: {
      message: "I want to collaborate on research projects and industry studies. Can you help me find research opportunities and partners?",
      context: { profileType: 'industry_expert', feature: 'research_collaboration' },
      suggestions: [
        "Find research projects",
        "Connect with researchers",
        "How do I contribute to studies?"
      ]
    },
    thought_leadership: {
      message: "I want to establish myself as a thought leader in my industry. Can you guide me on building my reputation and influence?",
      context: { profileType: 'industry_expert', feature: 'thought_leadership' },
      suggestions: [
        "Build thought leadership",
        "How do I create influential content?",
        "Find speaking opportunities"
      ]
    }
  },
  government: {
    policy_development: {
      message: "I'm working on policy development and need insights on innovation policies. Can you help me understand best practices and frameworks?",
      context: { profileType: 'government', feature: 'policy_development' },
      suggestions: [
        "Show innovation policies",
        "What are global best practices?",
        "How do I engage stakeholders?"
      ]
    },
    stakeholder_engagement: {
      message: "I need to engage with various stakeholders in the innovation ecosystem. Can you help me identify key players and engagement strategies?",
      context: { profileType: 'government', feature: 'stakeholder_engagement' },
      suggestions: [
        "Find key stakeholders",
        "How do I engage effectively?",
        "What are engagement best practices?"
      ]
    },
    innovation_programs: {
      message: "I want to develop and implement innovation programs for economic development. Can you provide guidance and examples?",
      context: { profileType: 'government', feature: 'innovation_programs' },
      suggestions: [
        "Design innovation programs",
        "Show successful examples",
        "How do I measure program success?"
      ]
    },
    public_private_partnerships: {
      message: "I'm exploring public-private partnerships for innovation initiatives. Can you help me understand partnership models and opportunities?",
      context: { profileType: 'government', feature: 'public_private_partnerships' },
      suggestions: [
        "Find partnership opportunities",
        "What are successful PPP models?",
        "How do I structure partnerships?"
      ]
    }
  },
  corporate: {
    innovation_strategy: {
      message: "I need to develop an innovation strategy for my organization. Can you help me with frameworks and best practices?",
      context: { profileType: 'corporate', feature: 'innovation_strategy' },
      suggestions: [
        "Create innovation strategy",
        "What frameworks work best?",
        "How do I measure innovation ROI?"
      ]
    },
    startup_partnerships: {
      message: "I want to explore partnerships with startups for innovation and growth. Can you help me find suitable startup partners?",
      context: { profileType: 'corporate', feature: 'startup_partnerships' },
      suggestions: [
        "Find startup partners",
        "How do I evaluate startups?",
        "What partnership models work?"
      ]
    },
    digital_transformation: {
      message: "I'm leading digital transformation initiatives in my organization. Can you provide guidance on technology adoption and change management?",
      context: { profileType: 'corporate', feature: 'digital_transformation' },
      suggestions: [
        "Plan digital transformation",
        "What technologies should I adopt?",
        "How do I manage change?"
      ]
    },
    talent_acquisition: {
      message: "I need to attract and retain innovative talent for my organization. Can you help me with talent acquisition strategies?",
      context: { profileType: 'corporate', feature: 'talent_acquisition' },
      suggestions: [
        "Find innovative talent",
        "How do I attract top performers?",
        "What retention strategies work?"
      ]
    }
  }
};

/**
 * Community and general platform triggers
 */
export const COMMUNITY_TRIGGERS = {
  content_discovery: {
    message: "I want to discover relevant content and discussions in the community. Can you help me find posts, events, and conversations that match my interests?",
    context: { feature: 'content_discovery', context: 'community' },
    suggestions: [
      "Show trending posts",
      "Find relevant discussions",
      "What events are happening?"
    ]
  },
  networking: {
    message: "I'm looking to expand my network and connect with like-minded innovators. Can you help me find people to connect with?",
    context: { feature: 'networking', context: 'community' },
    suggestions: [
      "Find people to connect with",
      "How do I start conversations?",
      "What networking events are available?"
    ]
  },
  collaboration: {
    message: "I want to find collaboration opportunities and join projects. Can you help me discover ways to collaborate with others?",
    context: { feature: 'collaboration', context: 'community' },
    suggestions: [
      "Find collaboration opportunities",
      "How do I join projects?",
      "What skills are needed?"
    ]
  },
  discover_opportunities: {
    message: "I want to discover opportunities relevant to my profile and interests. Can you help me find opportunities that match my skills and goals in the innovation ecosystem?",
    context: { feature: 'discover_opportunities', context: 'general' },
    suggestions: [
      "Show me relevant opportunities",
      "What opportunities match my profile?",
      "How do I apply for opportunities?"
    ]
  },
  events: {
    message: "I'm interested in attending innovation events and networking opportunities. Can you help me find upcoming events that would be valuable for my professional development?",
    context: { feature: 'events', context: 'general' },
    suggestions: [
      "Find upcoming events",
      "What networking events are available?",
      "How do I register for events?"
    ]
  },
  resources: {
    message: "I need access to knowledge resources and tools to support my innovation journey. Can you help me find relevant resources, guides, and educational materials?",
    context: { feature: 'resources', context: 'general' },
    suggestions: [
      "Find learning resources",
      "What tools are available?",
      "Show me educational materials"
    ]
  }
};

/**
 * Post creation assistance triggers
 */
export const POST_CREATION_TRIGGERS = {
  content_ideas: {
    message: "I want to create engaging content but need ideas. Can you help me brainstorm post topics that would resonate with the innovation community?",
    context: { feature: 'content_ideas', context: 'post_creation' },
    suggestions: [
      "Generate content ideas",
      "What topics are trending?",
      "How do I make posts engaging?"
    ]
  },
  writing_assistance: {
    message: "I need help writing and structuring my post. Can you provide guidance on creating compelling content that drives engagement?",
    context: { feature: 'writing_assistance', context: 'post_creation' },
    suggestions: [
      "Help me write better",
      "How do I structure posts?",
      "What makes content compelling?"
    ]
  },
  audience_targeting: {
    message: "I want to ensure my post reaches the right audience. Can you help me understand how to target and engage specific groups in the community?",
    context: { feature: 'audience_targeting', context: 'post_creation' },
    suggestions: [
      "Who should I target?",
      "How do I reach my audience?",
      "What hashtags should I use?"
    ]
  }
};

/**
 * AI Chat Trigger Service Class
 */
export class AIChatTriggerService {
  /**
   * Trigger AI chat with a predefined message
   */
  static async triggerChat(triggerKey: string, profileType?: string, additionalContext?: Record<string, any>): Promise<void> {
    const aiChatStore = useAIChatStore();
    
    let triggerData: AITriggerMessage | undefined;
    
    // Find the trigger message based on profile type and trigger key
    if (profileType && PROFILE_TRIGGERS[profileType as keyof typeof PROFILE_TRIGGERS]) {
      const profileTriggers = PROFILE_TRIGGERS[profileType as keyof typeof PROFILE_TRIGGERS];
      triggerData = profileTriggers[triggerKey as keyof typeof profileTriggers];
    }
    
    // Fallback to community triggers
    if (!triggerData && COMMUNITY_TRIGGERS[triggerKey as keyof typeof COMMUNITY_TRIGGERS]) {
      triggerData = COMMUNITY_TRIGGERS[triggerKey as keyof typeof COMMUNITY_TRIGGERS];
    }
    
    // Fallback to post creation triggers
    if (!triggerData && POST_CREATION_TRIGGERS[triggerKey as keyof typeof POST_CREATION_TRIGGERS]) {
      triggerData = POST_CREATION_TRIGGERS[triggerKey as keyof typeof POST_CREATION_TRIGGERS];
    }
    
    if (!triggerData) {
      console.warn('No trigger data found for:', { triggerKey, profileType });
      return;
    }
    
    // Open the chat
    aiChatStore.openChat();
    
    // Send the predefined message
    try {
      await aiChatStore.sendAIMessage(triggerData.message);
    } catch (error) {
      console.error('Error sending AI trigger message:', error);
    }
  }
  
  /**
   * Get available triggers for a profile type
   */
  static getTriggersForProfile(profileType: string): Record<string, AITriggerMessage> {
    return PROFILE_TRIGGERS[profileType as keyof typeof PROFILE_TRIGGERS] || {};
  }
  
  /**
   * Get all community triggers
   */
  static getCommunityTriggers(): Record<string, AITriggerMessage> {
    return COMMUNITY_TRIGGERS;
  }
  
  /**
   * Get all post creation triggers
   */
  static getPostCreationTriggers(): Record<string, AITriggerMessage> {
    return POST_CREATION_TRIGGERS;
  }
}

export default AIChatTriggerService;
