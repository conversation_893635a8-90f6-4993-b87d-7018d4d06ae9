/**
 * Service Coordination Plugin
 * 
 * Vue plugin for managing global service initialization and coordination
 * across the entire application lifecycle.
 */

import { App } from 'vue';
import { useGlobalServicesStore } from '../stores/globalServices';

export interface ServiceCoordinationConfig {
  enableAutoInitialization?: boolean;
  enableHealthMonitoring?: boolean;
  enableErrorRecovery?: boolean;
  healthCheckInterval?: number;
  maxRetryAttempts?: number;
  retryDelay?: number;
  enableDevTools?: boolean;
}

const defaultConfig: ServiceCoordinationConfig = {
  enableAutoInitialization: true,
  enableHealthMonitoring: true,
  enableErrorRecovery: true,
  healthCheckInterval: 30000, // 30 seconds
  maxRetryAttempts: 3,
  retryDelay: 2000, // 2 seconds
  enableDevTools: process.env.NODE_ENV === 'development'
};

class ServiceCoordinator {
  private config: ServiceCoordinationConfig;
  private healthCheckTimer: NodeJS.Timeout | null = null;
  private globalServices: ReturnType<typeof useGlobalServicesStore> | null = null;

  constructor(config: ServiceCoordinationConfig) {
    this.config = { ...defaultConfig, ...config };
  }

  async initialize(app: App): Promise<void> {
    console.log('ServiceCoordinator: Initializing service coordination');

    // Get the global services store
    this.globalServices = useGlobalServicesStore();

    // Auto-initialize services if enabled
    if (this.config.enableAutoInitialization) {
      await this.initializeServicesWithRetry();
    }

    // Start health monitoring if enabled
    if (this.config.enableHealthMonitoring) {
      this.startHealthMonitoring();
    }

    // Setup dev tools if enabled
    if (this.config.enableDevTools) {
      this.setupDevTools(app);
    }

    // Setup global error handlers
    this.setupErrorHandlers(app);

    console.log('ServiceCoordinator: Service coordination initialized successfully');
  }

  private async initializeServicesWithRetry(): Promise<void> {
    if (!this.globalServices) return;

    let attempts = 0;
    const maxAttempts = this.config.maxRetryAttempts || 3;

    while (attempts < maxAttempts) {
      try {
        attempts++;
        console.log(`ServiceCoordinator: Initializing services (attempt ${attempts}/${maxAttempts})`);

        await this.globalServices.initializeAllServices();

        // Validate service dependencies
        const validation = this.globalServices.validateServiceDependencies();
        if (!validation.valid) {
          throw new Error(`Service dependency validation failed: ${validation.issues.join(', ')}`);
        }

        console.log('ServiceCoordinator: All services initialized and validated successfully');
        return;

      } catch (error: any) {
        console.error(`ServiceCoordinator: Failed to initialize services (attempt ${attempts}):`, error);

        if (attempts < maxAttempts) {
          console.log(`ServiceCoordinator: Retrying in ${this.config.retryDelay}ms`);
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        } else {
          console.error('ServiceCoordinator: Max retry attempts reached, services may be unavailable');
          
          if (this.config.enableErrorRecovery) {
            console.log('ServiceCoordinator: Error recovery is enabled, will attempt recovery later');
          }
        }
      }
    }
  }

  private startHealthMonitoring(): void {
    if (!this.globalServices || this.healthCheckTimer) return;

    console.log('ServiceCoordinator: Starting health monitoring');

    this.healthCheckTimer = setInterval(() => {
      if (!this.globalServices) return;

      const health = this.globalServices.serviceHealth;

      if (health.status === 'UNHEALTHY') {
        console.warn('ServiceCoordinator: Services are unhealthy, attempting recovery');
        
        if (this.config.enableErrorRecovery) {
          this.globalServices.recoverFailedServices().catch(error => {
            console.error('ServiceCoordinator: Failed to recover services:', error);
          });
        }
      } else if (health.status === 'DEGRADED') {
        console.warn('ServiceCoordinator: Services are degraded:', health.failedServices);
      }

      // Log health status in development
      if (this.config.enableDevTools) {
        console.debug('ServiceCoordinator: Service health check:', health);
      }
    }, this.config.healthCheckInterval);
  }

  private stopHealthMonitoring(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
      console.log('ServiceCoordinator: Health monitoring stopped');
    }
  }

  private setupDevTools(app: App): void {
    // Add global properties for debugging
    app.config.globalProperties.$serviceCoordinator = this;
    app.config.globalProperties.$services = this.globalServices;

    // Add console commands for debugging
    if (typeof window !== 'undefined') {
      (window as any).__SERVICE_COORDINATOR__ = this;
      (window as any).__GLOBAL_SERVICES__ = this.globalServices;

      console.log('ServiceCoordinator: Dev tools enabled. Use __SERVICE_COORDINATOR__ and __GLOBAL_SERVICES__ in console');
    }
  }

  private setupErrorHandlers(app: App): void {
    // Global error handler for service-related errors
    app.config.errorHandler = (error: any, instance, info) => {
      console.error('ServiceCoordinator: Global error caught:', error, info);

      // Check if this is a service-related error
      if (error.message && error.message.includes('service')) {
        console.log('ServiceCoordinator: Service-related error detected, checking service health');
        
        if (this.globalServices && this.config.enableErrorRecovery) {
          this.globalServices.recoverFailedServices().catch(recoveryError => {
            console.error('ServiceCoordinator: Failed to recover from service error:', recoveryError);
          });
        }
      }
    };
  }

  async shutdown(): Promise<void> {
    console.log('ServiceCoordinator: Shutting down service coordination');

    this.stopHealthMonitoring();

    if (this.globalServices) {
      await this.globalServices.shutdownAllServices();
    }

    console.log('ServiceCoordinator: Service coordination shut down successfully');
  }

  // Public API for manual control
  getServiceHealth() {
    return this.globalServices?.serviceHealth;
  }

  async recoverServices(): Promise<void> {
    if (this.globalServices) {
      await this.globalServices.recoverFailedServices();
    }
  }

  async reinitializeServices(): Promise<void> {
    await this.initializeServicesWithRetry();
  }
}

// Plugin installation function
export function createServiceCoordination(config: ServiceCoordinationConfig = {}) {
  const coordinator = new ServiceCoordinator(config);

  return {
    install(app: App) {
      coordinator.initialize(app);

      // Provide the coordinator instance
      app.provide('serviceCoordinator', coordinator);
    },
    coordinator
  };
}

// Export for direct use
export { ServiceCoordinator };
